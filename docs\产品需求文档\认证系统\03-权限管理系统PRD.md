# 03-权限管理系统PRD

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**负责人**: 技术架构师  
**关联文档**: [总览文档](./README.md) | [用户认证系统](./02-用户认证系统PRD.md)

---

## 📋 **1. 权限模型设计**

### 1.1 RBAC权限模型

#### **权限模型架构**
```
用户(User) → 角色(Role) → 权限(Permission) → 资源(Resource)
```

#### **核心概念定义**

**用户(User)**
- 系统的实际使用者
- 可以分配一个或多个角色
- 可以拥有临时权限（有时效性）

**角色(Role)**
- 权限的集合，代表特定的职能
- 可以继承其他角色的权限
- 支持角色层级结构

**权限(Permission)**
- 对特定资源执行特定操作的授权
- 格式：`模块:操作:资源` (如 `sales:read:orders`)
- 支持通配符权限

**资源(Resource)**
- 系统中需要保护的对象
- 包括页面、API、数据、功能等

### 1.2 权限类型定义

#### **模块权限**
访问特定业务模块的权限
```typescript
interface ModulePermission {
  module: 'sales' | 'production' | 'warehouse' | 'finance' | 'admin'
  access: 'read' | 'write' | 'admin'
}

// 示例
const permissions = [
  'sales:read',      // 销售模块查看权限
  'sales:write',     // 销售模块编辑权限
  'production:admin' // 生产模块管理权限
]
```

#### **操作权限**
执行特定操作的权限
```typescript
interface OperationPermission {
  operation: 'create' | 'read' | 'update' | 'delete' | 'approve' | 'export'
  resource: string
}

// 示例
const permissions = [
  'sales:create:orders',    // 创建销售订单
  'sales:approve:orders',   // 审核销售订单
  'warehouse:export:inventory' // 导出库存数据
]
```

#### **数据权限**
访问特定范围数据的权限
```typescript
interface DataPermission {
  scope: 'self' | 'department' | 'company' | 'all'
  resource: string
}

// 示例
const dataScopes = [
  'sales:orders:self',       // 只能查看自己的订单
  'sales:orders:department', // 可以查看部门的订单
  'sales:orders:company'     // 可以查看公司所有订单
]
```

#### **字段权限**
查看或编辑特定字段的权限
```typescript
interface FieldPermission {
  field: string
  access: 'hidden' | 'readonly' | 'editable'
}

// 示例
const fieldPermissions = [
  'sales:orders:price:readonly',    // 价格字段只读
  'sales:orders:cost:hidden',       // 成本字段隐藏
  'sales:orders:discount:editable'  // 折扣字段可编辑
]
```

---

## 🔐 **2. 权限验证机制**

### 2.1 前端权限验证

#### **页面级权限控制**
```typescript
// src/components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredRole?: string
  dataScope?: string
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  dataScope,
  fallback
}) => {
  const { user, isAuthenticated } = useAuthStore()
  const { hasPermission, hasRole, hasDataScope } = usePermissions()
  
  // 未登录检查
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }
  
  // 权限检查
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return fallback || <NoPermissionPage />
  }
  
  // 角色检查
  if (requiredRole && !hasRole(requiredRole)) {
    return fallback || <NoPermissionPage />
  }
  
  // 数据范围检查
  if (dataScope && !hasDataScope(dataScope)) {
    return fallback || <NoPermissionPage />
  }
  
  return <>{children}</>
}

// 使用示例
<ProtectedRoute 
  requiredPermission="sales:read:orders"
  dataScope="department"
>
  <SalesOrderPage />
</ProtectedRoute>
```

#### **组件级权限控制**
```typescript
// src/components/auth/PermissionGuard.tsx
interface PermissionGuardProps {
  children: React.ReactNode
  permission: string
  fallback?: React.ReactNode
  hideWhenNoPermission?: boolean
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
  hideWhenNoPermission = false
}) => {
  const { hasPermission } = usePermissions()
  
  if (!hasPermission(permission)) {
    if (hideWhenNoPermission) {
      return null
    }
    return fallback || <span className="text-gray-400">无权限</span>
  }
  
  return <>{children}</>
}

// 使用示例
<PermissionGuard permission="sales:create:orders">
  <Button type="primary">新建订单</Button>
</PermissionGuard>
```

#### **Hook方式权限验证**
```typescript
// src/hooks/usePermissions.ts
export const usePermissions = () => {
  const { user } = useAuthStore()
  
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user || !user.permissions) return false
    
    // 支持通配符权限检查
    return user.permissions.some(p => 
      p === permission || 
      p.endsWith('*') && permission.startsWith(p.slice(0, -1))
    )
  }, [user])
  
  const hasRole = useCallback((role: string): boolean => {
    if (!user || !user.roles) return false
    return user.roles.some(r => r.roleCode === role)
  }, [user])
  
  const hasDataScope = useCallback((scope: string): boolean => {
    if (!user || !user.dataScopes) return false
    return user.dataScopes.includes(scope)
  }, [user])
  
  const hasFieldPermission = useCallback((field: string, access: 'read' | 'write'): boolean => {
    if (!user || !user.fieldPermissions) return false
    const fieldPerm = user.fieldPermissions[field]
    if (!fieldPerm) return false
    
    return access === 'read' 
      ? fieldPerm !== 'hidden'
      : fieldPerm === 'editable'
  }, [user])
  
  return {
    hasPermission,
    hasRole,
    hasDataScope,
    hasFieldPermission
  }
}

// 使用示例
const { hasPermission } = usePermissions()

if (hasPermission('sales:export:orders')) {
  // 显示导出按钮
}
```

### 2.2 后端权限验证

#### **API中间件权限验证**
```typescript
// src/middleware/auth.ts
export function requirePermission(permission: string) {
  return async (request: NextRequest) => {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    
    if (!token) {
      return NextResponse.json(
        { error: '未提供认证令牌' },
        { status: 401 }
      )
    }
    
    const user = await validateToken(token)
    if (!user) {
      return NextResponse.json(
        { error: '无效的认证令牌' },
        { status: 401 }
      )
    }
    
    if (!hasPermission(user, permission)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }
    
    // 将用户信息添加到请求上下文
    request.user = user
    return NextResponse.next()
  }
}

// 使用示例
export async function GET(request: NextRequest) {
  const authResult = await requirePermission('sales:read:orders')(request)
  if (authResult.status !== 200) {
    return authResult
  }
  
  // API逻辑
  const orders = await dataAccessManager.sales.getOrders({
    userId: request.user.id,
    dataScope: request.user.dataScope
  })
  
  return NextResponse.json(orders)
}
```

#### **DataAccessManager权限集成**
```typescript
// src/services/dataAccess/modules/SalesDataAccess.ts
export class SalesDataAccess {
  async getOrders(params: {
    userId: string
    permissions: string[]
    dataScope?: string
  }): Promise<ApiResponse<SalesOrder[]>> {
    
    // 权限验证
    if (!this.hasPermission(params.permissions, 'sales:read:orders')) {
      return this.createErrorResponse(
        'INSUFFICIENT_PERMISSIONS',
        '没有权限访问销售订单数据',
        403
      )
    }
    
    // 数据范围过滤
    const filters = this.buildDataScopeFilters(params.userId, params.dataScope)
    
    // 获取数据
    const orders = await this.repository.findOrders(filters)
    
    // 字段权限过滤
    const filteredOrders = this.applyFieldPermissions(orders, params.permissions)
    
    return this.createSuccessResponse(filteredOrders)
  }
  
  private buildDataScopeFilters(userId: string, dataScope?: string) {
    switch (dataScope) {
      case 'self':
        return { createdBy: userId }
      case 'department':
        return { departmentId: this.getUserDepartment(userId) }
      case 'company':
        return { companyId: this.getUserCompany(userId) }
      default:
        return {}
    }
  }
  
  private applyFieldPermissions(orders: SalesOrder[], permissions: string[]) {
    return orders.map(order => {
      const filteredOrder = { ...order }
      
      // 隐藏无权限查看的字段
      if (!this.hasPermission(permissions, 'sales:read:orders:cost')) {
        delete filteredOrder.cost
      }
      
      if (!this.hasPermission(permissions, 'sales:read:orders:profit')) {
        delete filteredOrder.profit
      }
      
      return filteredOrder
    })
  }
}
```

---

## 👥 **3. 角色管理功能**

### 3.1 角色管理界面

#### **用户故事**
> 作为系统管理员，我希望能够创建和管理不同的用户角色，为每个角色分配合适的权限，以便实现精细化的权限控制。

#### **功能规格**

| 功能 | 描述 | 验收标准 |
|------|------|----------|
| 角色列表 | 显示所有角色及其基本信息 | 列表正确显示，支持搜索和分页 |
| 创建角色 | 创建新角色并分配权限 | 角色创建成功，权限分配正确 |
| 编辑角色 | 修改角色信息和权限 | 修改保存成功，权限变更生效 |
| 删除角色 | 删除不再使用的角色 | 删除成功，关联用户处理正确 |
| 权限分配 | 为角色分配具体权限 | 权限树正确显示，分配结果准确 |

#### **角色管理页面组件**
```typescript
// src/app/admin/roles/page.tsx
export default function RolesManagementPage() {
  return (
    <ProtectedRoute requiredPermission="admin:roles:read">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">角色管理</h1>
          <PermissionGuard permission="admin:roles:create">
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setIsCreateModalVisible(true)}
            >
              新建角色
            </Button>
          </PermissionGuard>
        </div>
        
        <RolesList />
        <RoleCreateModal />
        <RoleEditModal />
        <PermissionAssignModal />
      </div>
    </ProtectedRoute>
  )
}
```

### 3.2 权限树组件

#### **权限树设计**
```typescript
// src/components/admin/PermissionTree.tsx
interface PermissionTreeProps {
  selectedPermissions: string[]
  onPermissionChange: (permissions: string[]) => void
  readonly?: boolean
}

export const PermissionTree: React.FC<PermissionTreeProps> = ({
  selectedPermissions,
  onPermissionChange,
  readonly = false
}) => {
  const permissionTree = useMemo(() => buildPermissionTree(), [])
  
  const handleCheck = (checkedKeys: string[]) => {
    if (!readonly) {
      onPermissionChange(checkedKeys)
    }
  }
  
  return (
    <Tree
      checkable
      checkedKeys={selectedPermissions}
      onCheck={handleCheck}
      treeData={permissionTree}
      disabled={readonly}
    />
  )
}

// 权限树数据结构
const buildPermissionTree = () => [
  {
    title: '销售管理',
    key: 'sales',
    children: [
      {
        title: '订单管理',
        key: 'sales:orders',
        children: [
          { title: '查看订单', key: 'sales:read:orders' },
          { title: '创建订单', key: 'sales:create:orders' },
          { title: '编辑订单', key: 'sales:update:orders' },
          { title: '删除订单', key: 'sales:delete:orders' },
          { title: '审核订单', key: 'sales:approve:orders' },
          { title: '导出订单', key: 'sales:export:orders' }
        ]
      },
      {
        title: '客户管理',
        key: 'sales:customers',
        children: [
          { title: '查看客户', key: 'sales:read:customers' },
          { title: '创建客户', key: 'sales:create:customers' },
          { title: '编辑客户', key: 'sales:update:customers' },
          { title: '删除客户', key: 'sales:delete:customers' }
        ]
      }
    ]
  },
  {
    title: '生产管理',
    key: 'production',
    children: [
      // 生产相关权限
    ]
  },
  {
    title: '系统管理',
    key: 'admin',
    children: [
      {
        title: '用户管理',
        key: 'admin:users',
        children: [
          { title: '查看用户', key: 'admin:read:users' },
          { title: '创建用户', key: 'admin:create:users' },
          { title: '编辑用户', key: 'admin:update:users' },
          { title: '删除用户', key: 'admin:delete:users' }
        ]
      },
      {
        title: '角色管理',
        key: 'admin:roles',
        children: [
          { title: '查看角色', key: 'admin:read:roles' },
          { title: '创建角色', key: 'admin:create:roles' },
          { title: '编辑角色', key: 'admin:update:roles' },
          { title: '删除角色', key: 'admin:delete:roles' }
        ]
      }
    ]
  }
]
```

### 3.3 用户角色分配

#### **功能描述**
- 为用户分配一个或多个角色
- 支持临时权限授权（有时效性）
- 支持权限继承和覆盖机制
- 提供权限变更审批流程

#### **用户角色分配组件**
```typescript
// src/components/admin/UserRoleAssignment.tsx
interface UserRoleAssignmentProps {
  userId: string
  currentRoles: Role[]
  onRoleChange: (roles: Role[]) => void
}

export const UserRoleAssignment: React.FC<UserRoleAssignmentProps> = ({
  userId,
  currentRoles,
  onRoleChange
}) => {
  const [availableRoles, setAvailableRoles] = useState<Role[]>([])
  const [selectedRoles, setSelectedRoles] = useState<string[]>(
    currentRoles.map(role => role.id)
  )
  
  useEffect(() => {
    loadAvailableRoles()
  }, [])
  
  const loadAvailableRoles = async () => {
    const result = await dataAccessManager.roles.getAll()
    if (result.status === 'success') {
      setAvailableRoles(result.data)
    }
  }
  
  const handleRoleChange = (roleIds: string[]) => {
    setSelectedRoles(roleIds)
    const roles = availableRoles.filter(role => roleIds.includes(role.id))
    onRoleChange(roles)
  }
  
  return (
    <div>
      <h3>角色分配</h3>
      <Select
        mode="multiple"
        style={{ width: '100%' }}
        placeholder="选择角色"
        value={selectedRoles}
        onChange={handleRoleChange}
      >
        {availableRoles.map(role => (
          <Option key={role.id} value={role.id}>
            {role.roleName}
          </Option>
        ))}
      </Select>
      
      <div className="mt-4">
        <h4>当前权限预览</h4>
        <PermissionTree
          selectedPermissions={calculateEffectivePermissions(selectedRoles)}
          onPermissionChange={() => {}}
          readonly
        />
      </div>
    </div>
  )
}
```

---

## ✅ **TODO清单**

### **阶段1: 权限模型实现** (1周)

#### **数据模型设计**
- [ ] 设计权限数据库表结构
- [ ] 创建TypeScript类型定义
- [ ] 实现权限种子数据
- [ ] 建立权限关系映射

#### **权限验证核心**
- [ ] 实现权限验证算法
- [ ] 开发权限继承机制
- [ ] 实现通配符权限支持
- [ ] 创建权限缓存机制

#### **前端权限组件**
- [ ] 开发ProtectedRoute组件
- [ ] 实现PermissionGuard组件
- [ ] 创建usePermissions Hook
- [ ] 实现权限状态管理

### **阶段2: 角色管理功能** (1周)

#### **角色管理界面**
- [ ] 创建角色列表页面
- [ ] 实现角色创建功能
- [ ] 开发角色编辑功能
- [ ] 实现角色删除功能

#### **权限分配功能**
- [ ] 开发权限树组件
- [ ] 实现权限选择功能
- [ ] 创建权限预览功能
- [ ] 实现批量权限操作

#### **用户角色分配**
- [ ] 开发用户角色分配界面
- [ ] 实现角色变更功能
- [ ] 创建权限变更审批流程
- [ ] 实现临时权限授权

### **阶段3: 后端权限验证** (1周)

#### **API权限中间件**
- [ ] 实现权限验证中间件
- [ ] 开发API权限装饰器
- [ ] 创建权限错误处理
- [ ] 实现权限日志记录

#### **DataAccessManager集成**
- [ ] 扩展数据访问权限验证
- [ ] 实现数据范围过滤
- [ ] 开发字段权限控制
- [ ] 创建权限缓存优化

#### **权限管理API**
- [ ] 实现角色CRUD API
- [ ] 开发权限分配API
- [ ] 创建用户角色管理API
- [ ] 实现权限查询API

### **阶段4: 测试和优化** (1周)

#### **功能测试**
- [ ] 编写权限验证测试用例
- [ ] 实现角色管理测试
- [ ] 进行权限继承测试
- [ ] 执行性能压力测试

#### **安全测试**
- [ ] 进行权限绕过测试
- [ ] 执行越权访问测试
- [ ] 测试权限缓存安全
- [ ] 验证权限日志完整性

#### **性能优化**
- [ ] 优化权限查询性能
- [ ] 实现权限缓存策略
- [ ] 优化权限树渲染
- [ ] 减少权限验证开销

---

## 📋 **验收标准**

### **功能验收**
- ✅ 权限模型正确实现，支持RBAC
- ✅ 角色管理功能完整可用
- ✅ 权限验证机制准确有效
- ✅ 用户角色分配功能正常

### **安全验收**
- ✅ 无权限绕过漏洞
- ✅ 权限验证覆盖所有接口
- ✅ 数据权限隔离有效
- ✅ 权限变更日志完整

### **性能验收**
- ✅ 权限验证响应时间 < 100ms
- ✅ 权限树加载时间 < 1秒
- ✅ 支持1000+权限节点
- ✅ 权限缓存命中率 > 90%

### **用户体验验收**
- ✅ 权限管理界面友好易用
- ✅ 权限分配操作简单直观
- ✅ 权限错误提示清晰准确
- ✅ 权限变更实时生效

---

**下一步**: 请查看 [04-技术架构设计PRD.md](./04-技术架构设计PRD.md) 了解技术架构的详细设计。

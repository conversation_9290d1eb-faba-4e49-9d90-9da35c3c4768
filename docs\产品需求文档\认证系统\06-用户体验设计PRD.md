# 06-用户体验设计PRD

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**负责人**: UX设计师  
**关联文档**: [总览文档](./README.md) | [安全性设计](./05-安全性设计PRD.md)

---

## 🎨 **1. 登录页面UI/UX规格**

### 1.1 页面布局设计

#### **设计原则**
- **简洁明了**: 突出核心登录功能，避免干扰元素
- **品牌一致**: 符合ERP系统整体设计语言
- **响应式设计**: 支持桌面端、平板端、移动端
- **无障碍访问**: 符合WCAG 2.1 AA标准
- **安全感知**: 通过视觉设计传达安全可信

#### **页面结构**
```typescript
// src/app/login/page.tsx
export default function LoginPage() {
  const [searchParams] = useSearchParams()
  const redirectUrl = searchParams.get('redirect') || '/dashboard'
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full opacity-20"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full opacity-20"></div>
      </div>
      
      {/* 主登录卡片 */}
      <div className="relative w-full max-w-md">
        <Card className="shadow-2xl border-0 backdrop-blur-sm bg-white/95">
          <div className="p-8">
            {/* Logo和标题区域 */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                欢迎回来
              </h1>
              <p className="text-gray-600">
                登录到企业资源规划系统
              </p>
            </div>
            
            {/* 登录表单 */}
            <LoginForm redirectUrl={redirectUrl} />
            
            {/* 帮助链接 */}
            <div className="mt-6 text-center space-y-2">
              <Link 
                href="/forgot-password" 
                className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
              >
                忘记密码？
              </Link>
              <div className="text-xs text-gray-500">
                需要帮助？联系 <a href="mailto:<EMAIL>" className="text-blue-600">技术支持</a>
              </div>
            </div>
          </div>
        </Card>
        
        {/* 安全提示 */}
        <div className="mt-4 text-center">
          <div className="inline-flex items-center text-xs text-gray-500">
            <Shield className="w-3 h-3 mr-1" />
            您的数据受到企业级安全保护
          </div>
        </div>
      </div>
    </div>
  )
}
```

### 1.2 登录表单组件

#### **表单设计规格**
```typescript
// src/components/auth/LoginForm.tsx
interface LoginFormProps {
  redirectUrl?: string
  onSuccess?: (user: User) => void
  onError?: (error: string) => void
}

export const LoginForm: React.FC<LoginFormProps> = ({ 
  redirectUrl = '/dashboard',
  onSuccess, 
  onError 
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [captchaRequired, setCaptchaRequired] = useState(false)
  const router = useRouter()
  
  const handleSubmit = async (values: LoginFormData) => {
    setLoading(true)
    try {
      const result = await dataAccessManager.auth.login({
        username: values.username,
        password: values.password,
        rememberMe: values.rememberMe,
        captcha: values.captcha
      })
      
      if (result.status === 'success') {
        message.success('登录成功')
        onSuccess?.(result.data.user)
        router.push(redirectUrl)
      } else {
        // 处理不同类型的错误
        handleLoginError(result)
        onError?.(result.message)
      }
    } catch (error) {
      const errorMessage = '登录失败，请稍后重试'
      message.error(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }
  
  const handleLoginError = (result: any) => {
    switch (result.code) {
      case 'INVALID_CREDENTIALS':
        message.error('用户名或密码错误')
        break
      case 'ACCOUNT_LOCKED':
        Modal.warning({
          title: '账户已锁定',
          content: `由于多次登录失败，您的账户已被锁定。请稍后重试或联系管理员。`,
          okText: '我知道了'
        })
        break
      case 'CAPTCHA_REQUIRED':
        setCaptchaRequired(true)
        message.warning('请输入验证码')
        break
      case 'PASSWORD_EXPIRED':
        Modal.info({
          title: '密码已过期',
          content: '您的密码已过期，请修改密码后重新登录。',
          okText: '去修改',
          onOk: () => router.push('/change-password')
        })
        break
      default:
        message.error(result.message || '登录失败')
    }
  }
  
  return (
    <Form
      form={form}
      name="login"
      onFinish={handleSubmit}
      layout="vertical"
      size="large"
      autoComplete="off"
    >
      {/* 用户名输入 */}
      <Form.Item
        name="username"
        label="用户名"
        rules={[
          { required: true, message: '请输入用户名' },
          { min: 3, message: '用户名至少3个字符' },
          { max: 50, message: '用户名最多50个字符' }
        ]}
      >
        <Input
          prefix={<User className="w-4 h-4 text-gray-400" />}
          placeholder="请输入用户名或邮箱"
          autoComplete="username"
          className="h-12"
        />
      </Form.Item>
      
      {/* 密码输入 */}
      <Form.Item
        name="password"
        label="密码"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码至少6个字符' }
        ]}
      >
        <Input.Password
          prefix={<Lock className="w-4 h-4 text-gray-400" />}
          placeholder="请输入密码"
          autoComplete="current-password"
          className="h-12"
          visibilityToggle={{
            visible: showPassword,
            onVisibleChange: setShowPassword
          }}
        />
      </Form.Item>
      
      {/* 验证码（条件显示） */}
      {captchaRequired && (
        <Form.Item
          name="captcha"
          label="验证码"
          rules={[
            { required: true, message: '请输入验证码' }
          ]}
        >
          <div className="flex space-x-2">
            <Input
              placeholder="请输入验证码"
              className="flex-1 h-12"
            />
            <CaptchaImage 
              onRefresh={() => form.setFieldsValue({ captcha: '' })}
              className="w-24 h-12"
            />
          </div>
        </Form.Item>
      )}
      
      {/* 记住我选项 */}
      <Form.Item name="rememberMe" valuePropName="checked" className="mb-6">
        <div className="flex items-center justify-between">
          <Checkbox>记住我</Checkbox>
          <Link 
            href="/forgot-password" 
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            忘记密码？
          </Link>
        </div>
      </Form.Item>
      
      {/* 登录按钮 */}
      <Form.Item className="mb-0">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          className="w-full h-12 text-base font-medium bg-gradient-to-r from-blue-600 to-indigo-600 border-0 hover:from-blue-700 hover:to-indigo-700"
        >
          {loading ? '登录中...' : '登录'}
        </Button>
      </Form.Item>
    </Form>
  )
}
```

### 1.3 验证码组件

#### **验证码设计**
```typescript
// src/components/auth/CaptchaImage.tsx
interface CaptchaImageProps {
  onRefresh?: () => void
  className?: string
}

export const CaptchaImage: React.FC<CaptchaImageProps> = ({ 
  onRefresh, 
  className 
}) => {
  const [captchaUrl, setCaptchaUrl] = useState('')
  const [loading, setLoading] = useState(false)
  
  const refreshCaptcha = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/captcha')
      const data = await response.json()
      setCaptchaUrl(data.imageUrl)
      onRefresh?.()
    } catch (error) {
      message.error('验证码加载失败')
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(() => {
    refreshCaptcha()
  }, [])
  
  return (
    <div className={`relative ${className}`}>
      {loading ? (
        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
          <Spin size="small" />
        </div>
      ) : (
        <img
          src={captchaUrl}
          alt="验证码"
          className="w-full h-full object-cover border border-gray-300 rounded cursor-pointer"
          onClick={refreshCaptcha}
          title="点击刷新验证码"
        />
      )}
      <Button
        type="text"
        size="small"
        icon={<RefreshCw className="w-3 h-3" />}
        className="absolute top-0 right-0 text-gray-500"
        onClick={refreshCaptcha}
        loading={loading}
      />
    </div>
  )
}
```

---

## 🔐 **2. 权限控制的用户交互设计**

### 2.1 权限控制组件

#### **页面级权限控制**
```typescript
// src/components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredRole?: string
  dataScope?: string
  fallback?: React.ReactNode
  loadingComponent?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  dataScope,
  fallback,
  loadingComponent
}) => {
  const { user, isAuthenticated, isLoading } = useAuthStore()
  const { hasPermission, hasRole, hasDataScope } = usePermissions()
  const router = useRouter()
  
  // 加载状态
  if (isLoading) {
    return loadingComponent || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Spin size="large" />
          <div className="mt-4 text-gray-600">正在验证权限...</div>
        </div>
      </div>
    )
  }
  
  // 未登录处理
  if (!isAuthenticated) {
    const currentPath = window.location.pathname
    router.push(`/login?redirect=${encodeURIComponent(currentPath)}`)
    return null
  }
  
  // 权限检查
  const permissionDenied = requiredPermission && !hasPermission(requiredPermission)
  const roleDenied = requiredRole && !hasRole(requiredRole)
  const scopeDenied = dataScope && !hasDataScope(dataScope)
  
  if (permissionDenied || roleDenied || scopeDenied) {
    return fallback || <NoPermissionPage 
      requiredPermission={requiredPermission}
      requiredRole={requiredRole}
      dataScope={dataScope}
    />
  }
  
  return <>{children}</>
}
```

#### **组件级权限控制**
```typescript
// src/components/auth/PermissionGuard.tsx
interface PermissionGuardProps {
  children: React.ReactNode
  permission: string
  fallback?: React.ReactNode
  hideWhenNoPermission?: boolean
  showTooltip?: boolean
  tooltipMessage?: string
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
  hideWhenNoPermission = false,
  showTooltip = true,
  tooltipMessage
}) => {
  const { hasPermission } = usePermissions()
  
  if (!hasPermission(permission)) {
    if (hideWhenNoPermission) {
      return null
    }
    
    if (fallback) {
      return <>{fallback}</>
    }
    
    // 默认的无权限显示
    const noPermissionElement = (
      <div className="inline-flex items-center text-gray-400 cursor-not-allowed">
        {React.cloneElement(children as React.ReactElement, {
          disabled: true,
          className: `${(children as React.ReactElement).props.className} opacity-50`
        })}
      </div>
    )
    
    if (showTooltip) {
      return (
        <Tooltip 
          title={tooltipMessage || '您没有权限执行此操作'}
          placement="top"
        >
          {noPermissionElement}
        </Tooltip>
      )
    }
    
    return noPermissionElement
  }
  
  return <>{children}</>
}

// 使用示例
<PermissionGuard 
  permission="sales:create:orders"
  tooltipMessage="您没有创建销售订单的权限"
>
  <Button type="primary" icon={<Plus />}>
    新建订单
  </Button>
</PermissionGuard>
```

### 2.2 权限管理界面

#### **角色管理页面**
```typescript
// src/app/admin/roles/page.tsx
export default function RolesManagementPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  
  return (
    <ProtectedRoute requiredPermission="admin:roles:read">
      <div className="p-6 max-w-7xl mx-auto">
        {/* 页面头部 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">角色管理</h1>
            <p className="text-gray-600 mt-1">管理系统角色和权限分配</p>
          </div>
          <PermissionGuard permission="admin:roles:create">
            <Button 
              type="primary" 
              icon={<Plus />}
              size="large"
              onClick={() => setIsCreateModalVisible(true)}
            >
              新建角色
            </Button>
          </PermissionGuard>
        </div>
        
        {/* 角色统计卡片 */}
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总角色数"
                value={roles.length}
                prefix={<Users className="w-4 h-4" />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="系统角色"
                value={roles.filter(r => r.isSystemRole).length}
                prefix={<Shield className="w-4 h-4" />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="自定义角色"
                value={roles.filter(r => !r.isSystemRole).length}
                prefix={<Settings className="w-4 h-4" />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="活跃角色"
                value={roles.filter(r => r.status === 'active').length}
                prefix={<CheckCircle className="w-4 h-4" />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
        
        {/* 角色列表 */}
        <Card title="角色列表" className="shadow-sm">
          <RolesList 
            roles={roles}
            loading={loading}
            onEdit={(role) => {
              setSelectedRole(role)
              setIsEditModalVisible(true)
            }}
            onDelete={handleDeleteRole}
            onViewPermissions={(role) => {
              setSelectedRole(role)
              // 显示权限详情
            }}
          />
        </Card>
        
        {/* 模态框 */}
        <RoleCreateModal
          visible={isCreateModalVisible}
          onCancel={() => setIsCreateModalVisible(false)}
          onSuccess={handleCreateSuccess}
        />
        
        <RoleEditModal
          visible={isEditModalVisible}
          role={selectedRole}
          onCancel={() => setIsEditModalVisible(false)}
          onSuccess={handleEditSuccess}
        />
      </div>
    </ProtectedRoute>
  )
}
```

#### **权限树组件**
```typescript
// src/components/admin/PermissionTree.tsx
interface PermissionTreeProps {
  selectedPermissions: string[]
  onPermissionChange: (permissions: string[]) => void
  readonly?: boolean
  showSearch?: boolean
}

export const PermissionTree: React.FC<PermissionTreeProps> = ({
  selectedPermissions,
  onPermissionChange,
  readonly = false,
  showSearch = true
}) => {
  const [searchValue, setSearchValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const [autoExpandParent, setAutoExpandParent] = useState(true)
  
  const permissionTree = useMemo(() => {
    const tree = buildPermissionTree()
    return searchValue ? filterTreeBySearch(tree, searchValue) : tree
  }, [searchValue])
  
  const handleCheck = (checkedKeys: any) => {
    if (!readonly) {
      onPermissionChange(checkedKeys.checked || checkedKeys)
    }
  }
  
  const handleExpand = (expandedKeys: string[]) => {
    setExpandedKeys(expandedKeys)
    setAutoExpandParent(false)
  }
  
  const handleSearch = (value: string) => {
    setSearchValue(value)
    if (value) {
      // 搜索时自动展开所有匹配的节点
      const matchedKeys = getMatchedKeys(permissionTree, value)
      setExpandedKeys(matchedKeys)
      setAutoExpandParent(true)
    }
  }
  
  return (
    <div className="permission-tree">
      {showSearch && (
        <div className="mb-4">
          <Input.Search
            placeholder="搜索权限..."
            allowClear
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full"
          />
        </div>
      )}
      
      <div className="border border-gray-200 rounded-lg p-4 max-h-96 overflow-auto">
        <Tree
          checkable={!readonly}
          checkedKeys={selectedPermissions}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onCheck={handleCheck}
          onExpand={handleExpand}
          treeData={permissionTree}
          className="permission-tree-content"
        />
      </div>
      
      {selectedPermissions.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm text-blue-800 font-medium mb-2">
            已选择 {selectedPermissions.length} 个权限
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedPermissions.slice(0, 10).map(permission => (
              <Tag key={permission} color="blue" className="text-xs">
                {getPermissionDisplayName(permission)}
              </Tag>
            ))}
            {selectedPermissions.length > 10 && (
              <Tag color="default" className="text-xs">
                +{selectedPermissions.length - 10} 更多
              </Tag>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
```

---

## ❌ **3. 错误处理和用户反馈机制**

### 3.1 错误页面设计

#### **403无权限页面**
```typescript
// src/app/403/page.tsx
interface NoPermissionPageProps {
  requiredPermission?: string
  requiredRole?: string
  dataScope?: string
}

export default function NoPermissionPage({
  requiredPermission,
  requiredRole,
  dataScope
}: NoPermissionPageProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  
  const getErrorMessage = () => {
    if (requiredPermission) {
      return `您需要 "${requiredPermission}" 权限才能访问此页面`
    }
    if (requiredRole) {
      return `您需要 "${requiredRole}" 角色才能访问此页面`
    }
    if (dataScope) {
      return `您的数据访问范围不足以查看此内容`
    }
    return '您没有权限访问此页面'
  }
  
  const handleContactAdmin = () => {
    const subject = encodeURIComponent('权限申请')
    const body = encodeURIComponent(
      `用户：${user?.fullName || user?.username}\n` +
      `申请权限：${requiredPermission || requiredRole || '页面访问权限'}\n` +
      `页面地址：${window.location.href}\n` +
      `申请理由：`
    )
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
  }
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <ShieldX className="w-12 h-12 text-red-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">403</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">访问被拒绝</h2>
          <p className="text-gray-600 mb-6">
            {getErrorMessage()}
          </p>
        </div>
        
        <div className="space-y-3">
          <Button 
            type="primary" 
            size="large"
            className="w-full"
            onClick={() => router.back()}
          >
            返回上页
          </Button>
          
          <Button 
            size="large"
            className="w-full"
            onClick={() => router.push('/dashboard')}
          >
            回到首页
          </Button>
          
          <Button 
            type="link"
            className="w-full"
            onClick={handleContactAdmin}
          >
            申请权限
          </Button>
        </div>
        
        <div className="mt-8 text-xs text-gray-500">
          如果您认为这是一个错误，请联系系统管理员
        </div>
      </div>
    </div>
  )
}
```

#### **401未认证页面**
```typescript
// src/app/401/page.tsx
export default function UnauthorizedPage() {
  const router = useRouter()
  const [countdown, setCountdown] = useState(5)
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          router.push('/login')
          return 0
        }
        return prev - 1
      })
    }, 1000)
    
    return () => clearInterval(timer)
  }, [router])
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-12 h-12 text-yellow-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">401</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">登录已过期</h2>
          <p className="text-gray-600 mb-6">
            您的登录状态已过期，请重新登录以继续使用系统
          </p>
        </div>
        
        <div className="space-y-3">
          <Button 
            type="primary" 
            size="large"
            className="w-full"
            onClick={() => router.push('/login')}
          >
            立即登录
          </Button>
          
          <div className="text-sm text-gray-500">
            {countdown > 0 ? `${countdown} 秒后自动跳转到登录页` : '正在跳转...'}
          </div>
        </div>
      </div>
    </div>
  )
}
```

### 3.2 用户反馈机制

#### **全局错误处理**
```typescript
// src/utils/auth/errorHandler.ts
export class AuthErrorHandler {
  static handleAuthError(error: AuthError, context?: string): void {
    const { code, message, details } = error
    
    switch (code) {
      case 'TOKEN_EXPIRED':
        this.handleTokenExpired()
        break
        
      case 'INSUFFICIENT_PERMISSIONS':
        this.handleInsufficientPermissions(details?.requiredPermission)
        break
        
      case 'ACCOUNT_LOCKED':
        this.handleAccountLocked(details)
        break
        
      case 'PASSWORD_EXPIRED':
        this.handlePasswordExpired()
        break
        
      case 'SUSPICIOUS_ACTIVITY':
        this.handleSuspiciousActivity(details)
        break
        
      default:
        this.handleGenericError(message, context)
    }
  }
  
  private static handleTokenExpired(): void {
    notification.warning({
      message: '登录已过期',
      description: '您的登录状态已过期，请重新登录',
      duration: 4,
      placement: 'topRight',
      onClick: () => {
        window.location.href = '/login'
      }
    })
    
    // 延迟跳转，给用户时间看到提示
    setTimeout(() => {
      window.location.href = '/login'
    }, 2000)
  }
  
  private static handleInsufficientPermissions(requiredPermission?: string): void {
    Modal.warning({
      title: '权限不足',
      content: (
        <div>
          <p>您没有权限执行此操作</p>
          {requiredPermission && (
            <p className="text-sm text-gray-600 mt-2">
              需要权限：<code>{requiredPermission}</code>
            </p>
          )}
          <p className="text-sm text-gray-600 mt-2">
            如需申请权限，请联系系统管理员
          </p>
        </div>
      ),
      okText: '我知道了',
      centered: true
    })
  }
  
  private static handleAccountLocked(details?: any): void {
    const lockDuration = details?.lockDuration || 1800 // 30分钟
    const unlockTime = new Date(Date.now() + lockDuration * 1000)
    
    Modal.error({
      title: '账户已锁定',
      content: (
        <div>
          <p>由于多次登录失败，您的账户已被锁定</p>
          <p className="text-sm text-gray-600 mt-2">
            解锁时间：{unlockTime.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600 mt-2">
            如需立即解锁，请联系系统管理员
          </p>
        </div>
      ),
      okText: '我知道了',
      centered: true
    })
  }
  
  private static handlePasswordExpired(): void {
    Modal.confirm({
      title: '密码已过期',
      content: '您的密码已过期，需要修改密码后才能继续使用系统',
      okText: '立即修改',
      cancelText: '稍后修改',
      centered: true,
      onOk: () => {
        window.location.href = '/change-password'
      }
    })
  }
  
  private static handleSuspiciousActivity(details?: any): void {
    notification.warning({
      message: '检测到异常活动',
      description: (
        <div>
          <p>系统检测到您的账户存在异常登录活动</p>
          {details?.reasons && (
            <ul className="text-sm mt-2">
              {details.reasons.map((reason: string, index: number) => (
                <li key={index}>• {reason}</li>
              ))}
            </ul>
          )}
          <p className="text-sm mt-2">
            如果不是您本人操作，请立即修改密码
          </p>
        </div>
      ),
      duration: 10,
      placement: 'topRight',
      btn: (
        <Button size="small" type="primary" onClick={() => {
          window.location.href = '/change-password'
        }}>
          修改密码
        </Button>
      )
    })
  }
  
  private static handleGenericError(message: string, context?: string): void {
    notification.error({
      message: context || '操作失败',
      description: message || '发生未知错误，请稍后重试',
      duration: 4,
      placement: 'topRight'
    })
  }
}
```

---

## ✅ **TODO清单**

### **阶段1: 登录界面开发** (1周)

#### **登录页面设计**
- [ ] 设计登录页面布局和样式
- [ ] 实现响应式设计适配
- [ ] 创建品牌视觉元素
- [ ] 添加背景装饰和动效

#### **登录表单开发**
- [ ] 实现登录表单组件
- [ ] 添加表单验证逻辑
- [ ] 集成验证码功能
- [ ] 实现记住我功能

#### **用户体验优化**
- [ ] 添加加载状态显示
- [ ] 实现错误提示机制
- [ ] 优化键盘导航支持
- [ ] 添加无障碍访问支持

### **阶段2: 权限控制界面** (1周)

#### **权限控制组件**
- [ ] 开发ProtectedRoute组件
- [ ] 实现PermissionGuard组件
- [ ] 创建权限加载状态
- [ ] 添加权限提示工具

#### **权限管理界面**
- [ ] 设计角色管理页面
- [ ] 实现权限树组件
- [ ] 开发用户角色分配界面
- [ ] 创建权限预览功能

#### **交互体验优化**
- [ ] 添加权限变更确认
- [ ] 实现批量操作界面
- [ ] 优化权限搜索功能
- [ ] 添加操作反馈提示

### **阶段3: 错误处理机制** (1周)

#### **错误页面设计**
- [ ] 创建403无权限页面
- [ ] 实现401未认证页面
- [ ] 设计500服务器错误页面
- [ ] 添加404页面未找到页面

#### **用户反馈机制**
- [ ] 实现全局错误处理器
- [ ] 开发智能错误提示
- [ ] 创建操作成功反馈
- [ ] 添加帮助和指导信息

#### **用户引导功能**
- [ ] 实现新用户引导流程
- [ ] 添加功能使用提示
- [ ] 创建帮助文档链接
- [ ] 实现快捷操作指南

### **阶段4: 体验优化和测试** (1周)

#### **性能优化**
- [ ] 优化页面加载速度
- [ ] 实现组件懒加载
- [ ] 优化图片和资源
- [ ] 减少不必要的重渲染

#### **用户体验测试**
- [ ] 进行可用性测试
- [ ] 执行无障碍访问测试
- [ ] 测试多设备兼容性
- [ ] 验证用户操作流程

#### **界面完善**
- [ ] 完善视觉设计细节
- [ ] 优化动画和过渡效果
- [ ] 统一交互行为
- [ ] 完善帮助文档

---

## 📋 **验收标准**

### **界面设计验收**
- ✅ 登录页面设计美观专业
- ✅ 响应式设计适配良好
- ✅ 品牌视觉一致性
- ✅ 用户界面友好易用

### **交互体验验收**
- ✅ 操作流程简洁直观
- ✅ 错误提示清晰准确
- ✅ 加载状态反馈及时
- ✅ 权限控制用户友好

### **无障碍访问验收**
- ✅ 符合WCAG 2.1 AA标准
- ✅ 键盘导航支持完整
- ✅ 屏幕阅读器兼容
- ✅ 色彩对比度符合要求

### **性能体验验收**
- ✅ 页面加载时间 < 3秒
- ✅ 交互响应时间 < 200ms
- ✅ 动画流畅无卡顿
- ✅ 内存使用合理

---

**下一步**: 请查看 [07-开发计划和验收PRD.md](./07-开发计划和验收PRD.md) 了解开发计划和验收标准的详细要求。

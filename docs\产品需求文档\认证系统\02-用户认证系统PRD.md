# 02-用户认证系统PRD

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**负责人**: 技术负责人  
**关联文档**: [总览文档](./README.md) | [产品概述](./01-产品概述PRD.md)

---

## 📋 **1. 用户登录功能**

### 1.1 登录功能规格

#### **用户故事**
> 作为一个ERP系统用户，我希望能够使用用户名和密码安全地登录系统，以便访问我有权限的功能模块。

#### **功能描述**
- 用户通过用户名/邮箱和密码进行身份验证
- 支持"记住我"功能，延长登录状态
- 提供密码强度验证和安全提示
- 支持多次登录失败后的账户锁定机制

#### **详细规格**

| 功能项 | 规格说明 | 验收标准 |
|--------|----------|----------|
| 登录表单 | 包含用户名、密码、记住我选项、验证码（可选） | 表单验证正确，UI符合设计规范 |
| 身份验证 | 验证用户名密码，生成JWT Token | 验证成功返回有效token，失败返回明确错误信息 |
| 会话管理 | Token有效期24小时，支持自动刷新 | Token过期前自动刷新，过期后跳转登录页 |
| 安全策略 | 5次失败锁定30分钟，记录登录日志 | 锁定机制生效，日志记录完整 |

#### **API接口设计**
```typescript
// POST /api/auth/login
interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
  captcha?: string
}

interface LoginResponse {
  status: 'success' | 'error'
  data: {
    user: User
    accessToken: string
    refreshToken: string
    expiresIn: number
  }
  message: string
}
```

### 1.2 登出功能规格

#### **用户故事**
> 作为一个已登录的用户，我希望能够安全地退出系统，确保我的会话被完全清除。

#### **功能描述**
- 清除客户端存储的所有认证信息
- 使服务端Token失效
- 重定向到登录页面
- 记录登出操作日志

#### **API接口设计**
```typescript
// POST /api/auth/logout
interface LogoutRequest {
  refreshToken: string
}

interface LogoutResponse {
  status: 'success'
  message: string
}
```

---

## 🔐 **2. 会话管理**

### 2.1 会话生命周期

#### **会话策略**
- **默认会话时长**: 8小时（工作时间）
- **最大会话时长**: 24小时
- **空闲超时**: 2小时无操作自动登出
- **并发会话**: 同一用户最多3个活跃会话

#### **会话状态管理**
```typescript
interface SessionState {
  sessionId: string
  userId: string
  createdAt: string
  lastActivityAt: string
  expiresAt: string
  isActive: boolean
  deviceInfo: {
    userAgent: string
    ipAddress: string
    deviceType: 'desktop' | 'mobile' | 'tablet'
  }
}
```

### 2.2 Token管理策略

#### **JWT Token设计**
```typescript
interface JWTPayload {
  userId: string
  username: string
  roles: string[]
  permissions: string[]
  sessionId: string
  iat: number
  exp: number
}
```

#### **Token刷新机制**
- **Access Token有效期**: 1小时
- **Refresh Token有效期**: 7天
- **自动刷新**: Token过期前5分钟自动刷新
- **静默刷新**: 后台自动处理，用户无感知

#### **Token存储策略**
```typescript
// Token存储配置
export const TOKEN_STORAGE = {
  accessToken: {
    storage: 'memory', // 内存存储，页面刷新后丢失
    key: 'access_token'
  },
  refreshToken: {
    storage: 'httpOnly', // HttpOnly Cookie，防XSS
    key: 'refresh_token',
    secure: true,
    sameSite: 'strict'
  }
}
```

---

## 🔒 **3. 密码安全策略**

### 3.1 密码要求

#### **密码复杂度规则**
- **最小长度**: 8位
- **最大长度**: 128位
- **必须包含**: 大写字母、小写字母、数字
- **可选包含**: 特殊字符 (!@#$%^&*)
- **禁止内容**: 用户名、常见密码、连续字符

#### **密码历史策略**
- 不能重复最近5次使用的密码
- 密码有效期90天（可配置）
- 到期前7天开始提醒用户修改

### 3.2 密码存储和验证

#### **密码加密**
```typescript
export class PasswordManager {
  private static readonly SALT_ROUNDS = 12
  
  static async hashPassword(password: string): Promise<{hash: string, salt: string}> {
    const salt = await bcrypt.genSalt(this.SALT_ROUNDS)
    const hash = await bcrypt.hash(password, salt)
    return { hash, salt }
  }
  
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }
  
  static validatePasswordStrength(password: string): {
    isValid: boolean
    errors: string[]
    score: number
  } {
    // 密码强度验证逻辑
  }
}
```

### 3.3 账户安全策略

#### **登录失败处理**
- **失败阈值**: 5次连续失败
- **锁定时间**: 30分钟
- **锁定策略**: 指数退避（首次5分钟，递增到30分钟）
- **解锁方式**: 时间自动解锁或管理员手动解锁

#### **异常检测**
- **异地登录检测**: IP地址变化超过阈值
- **设备变更检测**: User-Agent显著变化
- **时间异常检测**: 非正常工作时间登录
- **频率异常检测**: 短时间内多次登录尝试

---

## 🔧 **4. DataAccessManager集成**

### 4.1 认证服务集成

#### **AuthDataAccess模块**
```typescript
// src/services/dataAccess/modules/AuthDataAccess.ts
export class AuthDataAccess {
  async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResult>> {
    // 实现登录逻辑
    const response = await this.apiClient.post('/auth/login', credentials)
    return this.handleResponse(response)
  }
  
  async logout(refreshToken: string): Promise<ApiResponse<void>> {
    // 实现登出逻辑
    const response = await this.apiClient.post('/auth/logout', { refreshToken })
    return this.handleResponse(response)
  }
  
  async refreshToken(refreshToken: string): Promise<ApiResponse<TokenResult>> {
    // 实现token刷新
    const response = await this.apiClient.post('/auth/refresh', { refreshToken })
    return this.handleResponse(response)
  }
  
  async validateToken(token: string): Promise<ApiResponse<User>> {
    // 实现token验证
    const response = await this.apiClient.get('/auth/validate', {
      headers: { Authorization: `Bearer ${token}` }
    })
    return this.handleResponse(response)
  }
}
```

#### **DataAccessManager注册**
```typescript
// src/services/dataAccess/DataAccessManager.ts
export class DataAccessManager {
  public readonly auth = new AuthDataAccess()
  // ... 其他模块
  
  constructor() {
    this.initializeAuth()
  }
  
  private initializeAuth() {
    // 初始化认证模块
    this.auth.setApiClient(this.apiClient)
    this.auth.setErrorHandler(this.errorHandler)
  }
}
```

### 4.2 状态管理集成

#### **Zustand Store扩展**
```typescript
// src/store/useAuthStore.ts
interface AuthState {
  // 认证状态
  isAuthenticated: boolean
  user: User | null
  accessToken: string | null
  
  // 会话状态
  sessionId: string | null
  lastActivity: number
  
  // 登录状态
  isLoading: boolean
  loginError: string | null
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  updateLastActivity: () => void
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        isAuthenticated: false,
        user: null,
        accessToken: null,
        sessionId: null,
        lastActivity: Date.now(),
        isLoading: false,
        loginError: null,
        
        // Actions实现
        login: async (credentials) => {
          set({ isLoading: true, loginError: null })
          try {
            const result = await dataAccessManager.auth.login(credentials)
            if (result.status === 'success') {
              set({
                isAuthenticated: true,
                user: result.data.user,
                accessToken: result.data.accessToken,
                sessionId: result.data.sessionId,
                lastActivity: Date.now(),
                isLoading: false
              })
            } else {
              set({ loginError: result.message, isLoading: false })
            }
          } catch (error) {
            set({ loginError: '登录失败', isLoading: false })
          }
        },
        
        logout: async () => {
          const { refreshToken } = get()
          if (refreshToken) {
            await dataAccessManager.auth.logout(refreshToken)
          }
          set({
            isAuthenticated: false,
            user: null,
            accessToken: null,
            sessionId: null,
            loginError: null
          })
        },
        
        refreshToken: async () => {
          // Token刷新逻辑
        },
        
        updateLastActivity: () => {
          set({ lastActivity: Date.now() })
        }
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          // 只持久化必要的状态
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          sessionId: state.sessionId
        })
      }
    )
  )
)
```

---

## ✅ **TODO清单**

### **阶段1: 基础认证功能** (1周)

#### **登录功能开发**
- [ ] 创建登录页面组件 (`src/app/login/page.tsx`)
- [ ] 实现登录表单组件 (`src/components/auth/LoginForm.tsx`)
- [ ] 开发登录API端点 (`src/app/api/auth/login/route.ts`)
- [ ] 实现JWT Token生成和验证
- [ ] 集成DataAccessManager认证模块

#### **登出功能开发**
- [ ] 实现登出API端点 (`src/app/api/auth/logout/route.ts`)
- [ ] 开发登出功能组件
- [ ] 实现Token失效机制
- [ ] 添加登出确认对话框

#### **会话管理**
- [ ] 实现会话状态管理
- [ ] 开发Token自动刷新机制
- [ ] 实现会话超时检测
- [ ] 添加会话活动跟踪

### **阶段2: 安全策略实现** (1周)

#### **密码安全**
- [ ] 实现密码强度验证
- [ ] 开发密码加密存储
- [ ] 实现密码历史检查
- [ ] 添加密码修改功能

#### **账户安全**
- [ ] 实现登录失败锁定机制
- [ ] 开发异常登录检测
- [ ] 实现验证码功能（可选）
- [ ] 添加安全日志记录

#### **Token安全**
- [ ] 实现Token安全存储
- [ ] 开发Token刷新策略
- [ ] 实现Token撤销机制
- [ ] 添加Token泄露检测

### **阶段3: 集成和优化** (1周)

#### **DataAccessManager集成**
- [ ] 完善AuthDataAccess模块
- [ ] 集成错误处理机制
- [ ] 实现缓存策略
- [ ] 添加性能监控

#### **状态管理优化**
- [ ] 完善Zustand Store
- [ ] 实现状态持久化
- [ ] 添加状态同步机制
- [ ] 优化内存使用

#### **用户体验优化**
- [ ] 实现加载状态显示
- [ ] 添加错误提示机制
- [ ] 优化页面跳转逻辑
- [ ] 实现记住登录状态

### **阶段4: 测试和文档** (1周)

#### **功能测试**
- [ ] 编写单元测试用例
- [ ] 实现集成测试
- [ ] 进行安全测试
- [ ] 执行性能测试

#### **文档编写**
- [ ] 完善API文档
- [ ] 编写用户操作手册
- [ ] 创建开发者指南
- [ ] 准备部署文档

#### **代码审查**
- [ ] 进行代码质量检查
- [ ] 执行安全代码审查
- [ ] 优化代码性能
- [ ] 完善错误处理

---

## 📋 **验收标准**

### **功能验收**
- ✅ 用户能够成功登录和登出
- ✅ Token管理机制正常工作
- ✅ 会话超时机制正确触发
- ✅ 密码安全策略有效执行

### **安全验收**
- ✅ 密码加密存储，无明文泄露
- ✅ Token无法被伪造或篡改
- ✅ 登录失败锁定机制生效
- ✅ 异常登录能够被检测和记录

### **性能验收**
- ✅ 登录响应时间 < 2秒
- ✅ Token验证时间 < 100ms
- ✅ 并发登录支持 > 100用户
- ✅ 内存使用合理，无泄露

### **集成验收**
- ✅ DataAccessManager集成正常
- ✅ Zustand状态管理正确
- ✅ 现有页面路由保护生效
- ✅ API接口规范符合项目标准

---

**下一步**: 请查看 [03-权限管理系统PRD.md](./03-权限管理系统PRD.md) 了解权限管理系统的详细设计。

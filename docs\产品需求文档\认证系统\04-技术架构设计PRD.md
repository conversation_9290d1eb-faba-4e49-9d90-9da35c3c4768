# 04-技术架构设计PRD

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**负责人**: 技术架构师  
**关联文档**: [总览文档](./README.md) | [权限管理系统](./03-权限管理系统PRD.md)

---

## 🏗️ **1. 架构设计要求**

### 1.1 DataAccessManager集成

#### **认证服务集成架构**
```typescript
// src/services/dataAccess/modules/AuthDataAccess.ts
export class AuthDataAccess extends BaseDataAccess {
  private tokenManager: TokenManager
  private sessionManager: SessionManager
  private permissionCache: PermissionCache
  
  constructor() {
    super()
    this.tokenManager = new TokenManager()
    this.sessionManager = new SessionManager()
    this.permissionCache = new PermissionCache()
  }
  
  async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResult>> {
    try {
      // 1. 验证用户凭据
      const user = await this.validateCredentials(credentials)
      if (!user) {
        return this.createErrorResponse('INVALID_CREDENTIALS', '用户名或密码错误')
      }
      
      // 2. 检查账户状态
      if (user.status === 'locked') {
        return this.createErrorResponse('ACCOUNT_LOCKED', '账户已被锁定')
      }
      
      // 3. 生成Token和会话
      const tokens = await this.tokenManager.generateTokens(user)
      const session = await this.sessionManager.createSession(user, tokens)
      
      // 4. 加载用户权限
      const permissions = await this.loadUserPermissions(user.id)
      
      // 5. 记录登录日志
      await this.auditLogger.logLogin(user, session)
      
      return this.createSuccessResponse({
        user: { ...user, permissions },
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        sessionId: session.id,
        expiresIn: tokens.expiresIn
      })
    } catch (error) {
      return this.handleError(error, '登录失败')
    }
  }
  
  async refreshToken(refreshToken: string): Promise<ApiResponse<TokenResult>> {
    try {
      // 1. 验证Refresh Token
      const session = await this.sessionManager.validateRefreshToken(refreshToken)
      if (!session) {
        return this.createErrorResponse('INVALID_TOKEN', '无效的刷新令牌')
      }
      
      // 2. 检查会话状态
      if (!session.isActive || session.expiresAt < new Date()) {
        return this.createErrorResponse('SESSION_EXPIRED', '会话已过期')
      }
      
      // 3. 生成新的Access Token
      const user = await this.getUserById(session.userId)
      const newTokens = await this.tokenManager.generateTokens(user)
      
      // 4. 更新会话
      await this.sessionManager.updateSession(session.id, {
        lastActivityAt: new Date(),
        accessToken: newTokens.accessToken
      })
      
      return this.createSuccessResponse({
        accessToken: newTokens.accessToken,
        expiresIn: newTokens.expiresIn
      })
    } catch (error) {
      return this.handleError(error, 'Token刷新失败')
    }
  }
  
  async validateToken(token: string): Promise<ApiResponse<User>> {
    try {
      // 1. 验证Token格式和签名
      const payload = await this.tokenManager.verifyToken(token)
      if (!payload) {
        return this.createErrorResponse('INVALID_TOKEN', '无效的访问令牌')
      }
      
      // 2. 检查Token是否在黑名单中
      const isBlacklisted = await this.tokenManager.isTokenBlacklisted(token)
      if (isBlacklisted) {
        return this.createErrorResponse('TOKEN_REVOKED', '令牌已被撤销')
      }
      
      // 3. 获取用户信息和权限
      const user = await this.getUserWithPermissions(payload.userId)
      if (!user) {
        return this.createErrorResponse('USER_NOT_FOUND', '用户不存在')
      }
      
      // 4. 更新最后活动时间
      await this.sessionManager.updateLastActivity(payload.sessionId)
      
      return this.createSuccessResponse(user)
    } catch (error) {
      return this.handleError(error, 'Token验证失败')
    }
  }
  
  private async loadUserPermissions(userId: string): Promise<Permission[]> {
    // 从缓存获取权限
    const cached = await this.permissionCache.getUserPermissions(userId)
    if (cached) {
      return cached
    }
    
    // 从数据库加载权限
    const permissions = await this.repository.getUserPermissions(userId)
    
    // 缓存权限
    await this.permissionCache.setUserPermissions(userId, permissions)
    
    return permissions
  }
}
```

#### **DataAccessManager注册**
```typescript
// src/services/dataAccess/DataAccessManager.ts
export class DataAccessManager {
  public readonly auth = new AuthDataAccess()
  public readonly users = new UsersDataAccess()
  public readonly roles = new RolesDataAccess()
  public readonly permissions = new PermissionsDataAccess()
  // ... 其他模块
  
  constructor() {
    this.initializeModules()
    this.setupInterceptors()
  }
  
  private initializeModules() {
    // 初始化认证模块
    this.auth.setApiClient(this.apiClient)
    this.auth.setErrorHandler(this.errorHandler)
    this.auth.setLogger(this.logger)
    
    // 设置模块间依赖
    this.users.setAuthModule(this.auth)
    this.roles.setAuthModule(this.auth)
  }
  
  private setupInterceptors() {
    // 请求拦截器 - 添加认证头
    this.apiClient.interceptors.request.use((config) => {
      const token = this.auth.getAccessToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
    
    // 响应拦截器 - 处理认证错误
    this.apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token过期，尝试刷新
          const refreshed = await this.auth.tryRefreshToken()
          if (refreshed) {
            // 重试原请求
            return this.apiClient.request(error.config)
          } else {
            // 刷新失败，跳转登录
            this.auth.redirectToLogin()
          }
        }
        return Promise.reject(error)
      }
    )
  }
}
```

### 1.2 权限验证中间件

#### **Next.js中间件设计**
```typescript
// middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { validateToken, checkPagePermission } from '@/utils/auth'

// 公开路径配置
const PUBLIC_PATHS = [
  '/login',
  '/forgot-password',
  '/api/auth/login',
  '/api/auth/refresh',
  '/_next',
  '/favicon.ico'
]

// 页面权限配置
const PAGE_PERMISSIONS = {
  '/dashboard': 'dashboard:read',
  '/sales': 'sales:read',
  '/sales/orders': 'sales:read:orders',
  '/sales/customers': 'sales:read:customers',
  '/production': 'production:read',
  '/warehouse': 'warehouse:read',
  '/admin': 'admin:read',
  '/admin/users': 'admin:read:users',
  '/admin/roles': 'admin:read:roles'
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // 检查是否为公开路径
  if (isPublicPath(pathname)) {
    return NextResponse.next()
  }
  
  // 获取认证Token
  const token = getTokenFromRequest(request)
  if (!token) {
    return redirectToLogin(request)
  }
  
  // 验证Token
  const user = await validateToken(token)
  if (!user) {
    return redirectToLogin(request)
  }
  
  // 检查页面权限
  const requiredPermission = getRequiredPermission(pathname)
  if (requiredPermission && !hasPermission(user, requiredPermission)) {
    return NextResponse.redirect(new URL('/403', request.url))
  }
  
  // 添加用户信息到请求头
  const response = NextResponse.next()
  response.headers.set('x-user-id', user.id)
  response.headers.set('x-user-permissions', JSON.stringify(user.permissions))
  
  return response
}

function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some(path => 
    pathname.startsWith(path) || pathname === path
  )
}

function getTokenFromRequest(request: NextRequest): string | null {
  // 优先从Authorization头获取
  const authHeader = request.headers.get('authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  // 从Cookie获取
  return request.cookies.get('access-token')?.value || null
}

function getRequiredPermission(pathname: string): string | null {
  // 精确匹配
  if (PAGE_PERMISSIONS[pathname]) {
    return PAGE_PERMISSIONS[pathname]
  }
  
  // 模糊匹配
  for (const [path, permission] of Object.entries(PAGE_PERMISSIONS)) {
    if (pathname.startsWith(path + '/')) {
      return permission
    }
  }
  
  return null
}

function hasPermission(user: any, permission: string): boolean {
  if (!user.permissions) return false
  
  return user.permissions.some((p: string) => 
    p === permission || 
    p === '*' || 
    (p.endsWith('*') && permission.startsWith(p.slice(0, -1)))
  )
}

function redirectToLogin(request: NextRequest): NextResponse {
  const loginUrl = new URL('/login', request.url)
  loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
  return NextResponse.redirect(loginUrl)
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
```

---

## 📡 **2. API接口设计规范**

### 2.1 认证相关API

#### **API路径规范**
```
/api/auth/
├── login          # POST - 用户登录
├── logout         # POST - 用户登出
├── refresh        # POST - 刷新token
├── validate       # GET - 验证token
├── profile        # GET - 获取用户信息
├── change-password # POST - 修改密码
└── reset-password  # POST - 重置密码

/api/users/
├── /              # GET - 获取用户列表, POST - 创建用户
├── /[id]          # GET - 获取用户详情, PUT - 更新用户, DELETE - 删除用户
├── /[id]/roles    # GET - 获取用户角色, PUT - 分配角色
├── /[id]/permissions # GET - 获取用户权限
└── /[id]/sessions # GET - 获取用户会话, DELETE - 终止会话

/api/roles/
├── /              # GET - 获取角色列表, POST - 创建角色
├── /[id]          # GET - 获取角色详情, PUT - 更新角色, DELETE - 删除角色
├── /[id]/permissions # GET - 获取角色权限, PUT - 分配权限
└── /[id]/users    # GET - 获取角色用户列表

/api/permissions/
├── /              # GET - 获取权限列表
├── /modules       # GET - 获取模块权限树
├── /check         # POST - 检查用户权限
└── /effective     # GET - 获取用户有效权限
```

#### **统一响应格式**
```typescript
// 基础响应接口
interface ApiResponse<T> {
  status: 'success' | 'error'
  data: T | null
  message: string
  code: string
  timestamp: string
  requestId: string
}

// 分页响应接口
interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    current: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 错误响应接口
interface ErrorResponse {
  status: 'error'
  data: null
  message: string
  code: string
  details?: any
  timestamp: string
  requestId: string
}

// 成功响应示例
{
  "status": "success",
  "data": {
    "user": {
      "id": "user-123",
      "username": "admin",
      "fullName": "系统管理员",
      "roles": ["admin"],
      "permissions": ["*"]
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 3600
  },
  "message": "登录成功",
  "code": "LOGIN_SUCCESS",
  "timestamp": "2025-07-31T10:30:00Z",
  "requestId": "req-12345"
}

// 错误响应示例
{
  "status": "error",
  "data": null,
  "message": "用户名或密码错误",
  "code": "INVALID_CREDENTIALS",
  "details": {
    "field": "password",
    "attempts": 3,
    "remainingAttempts": 2
  },
  "timestamp": "2025-07-31T10:30:00Z",
  "requestId": "req-12346"
}
```

### 2.2 API实现示例

#### **登录API实现**
```typescript
// src/app/api/auth/login/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { validateLoginRequest, createApiResponse } from '@/utils/api'

export async function POST(request: NextRequest) {
  try {
    // 1. 解析请求体
    const body = await request.json()
    
    // 2. 验证请求参数
    const validation = validateLoginRequest(body)
    if (!validation.isValid) {
      return NextResponse.json(
        createApiResponse('error', null, '请求参数无效', 'INVALID_REQUEST', {
          errors: validation.errors
        }),
        { status: 400 }
      )
    }
    
    // 3. 调用认证服务
    const result = await dataAccessManager.auth.login({
      username: body.username,
      password: body.password,
      rememberMe: body.rememberMe || false,
      captcha: body.captcha,
      clientInfo: {
        userAgent: request.headers.get('user-agent') || '',
        ipAddress: getClientIP(request)
      }
    })
    
    // 4. 处理响应
    if (result.status === 'success') {
      const response = NextResponse.json(result)
      
      // 设置HttpOnly Cookie
      response.cookies.set('refresh-token', result.data.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 // 7天
      })
      
      return response
    } else {
      return NextResponse.json(result, { 
        status: result.code === 'INVALID_CREDENTIALS' ? 401 : 400 
      })
    }
  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json(
      createApiResponse('error', null, '服务器内部错误', 'INTERNAL_ERROR'),
      { status: 500 }
    )
  }
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return request.ip || 'unknown'
}
```

#### **权限检查API实现**
```typescript
// src/app/api/permissions/check/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { requireAuth, createApiResponse } from '@/utils/api'

export async function POST(request: NextRequest) {
  try {
    // 1. 认证检查
    const authResult = await requireAuth(request)
    if (!authResult.success) {
      return NextResponse.json(authResult.response, { status: authResult.status })
    }
    
    const user = authResult.user
    
    // 2. 解析请求体
    const body = await request.json()
    const { permissions, resource, context } = body
    
    // 3. 权限检查
    const checkResults = await Promise.all(
      permissions.map(async (permission: string) => {
        const hasPermission = await dataAccessManager.permissions.checkUserPermission(
          user.id,
          permission,
          resource,
          context
        )
        
        return {
          permission,
          granted: hasPermission.status === 'success' && hasPermission.data
        }
      })
    )
    
    // 4. 返回结果
    return NextResponse.json(
      createApiResponse('success', {
        userId: user.id,
        permissions: checkResults,
        checkedAt: new Date().toISOString()
      }, '权限检查完成', 'PERMISSION_CHECK_SUCCESS')
    )
  } catch (error) {
    console.error('Permission check API error:', error)
    return NextResponse.json(
      createApiResponse('error', null, '权限检查失败', 'PERMISSION_CHECK_ERROR'),
      { status: 500 }
    )
  }
}
```

---

## 🗄️ **3. 数据库模型设计**

### 3.1 数据库表结构

#### **用户相关表**
```sql
-- 用户表
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  salt VARCHAR(32) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  avatar_url VARCHAR(255),
  phone VARCHAR(20),
  department_id VARCHAR(36),
  employee_id VARCHAR(20),
  status ENUM('active', 'inactive', 'locked') DEFAULT 'active',
  last_login_at TIMESTAMP NULL,
  login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,
  password_expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(36),
  updated_by VARCHAR(36),
  
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_status (status),
  INDEX idx_department (department_id)
);

-- 用户密码历史表
CREATE TABLE user_password_history (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_created (user_id, created_at)
);
```

#### **角色权限表**
```sql
-- 角色表
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY,
  role_code VARCHAR(50) UNIQUE NOT NULL,
  role_name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_role_id VARCHAR(36),
  is_system_role BOOLEAN DEFAULT FALSE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(36),
  updated_by VARCHAR(36),
  
  FOREIGN KEY (parent_role_id) REFERENCES roles(id) ON DELETE SET NULL,
  INDEX idx_role_code (role_code),
  INDEX idx_status (status),
  INDEX idx_parent (parent_role_id)
);

-- 权限表
CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY,
  permission_code VARCHAR(100) UNIQUE NOT NULL,
  permission_name VARCHAR(100) NOT NULL,
  module VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  resource VARCHAR(100),
  description TEXT,
  is_system_permission BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_permission_code (permission_code),
  INDEX idx_module (module),
  INDEX idx_module_action (module, action)
);

-- 用户角色关联表
CREATE TABLE user_roles (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  role_id VARCHAR(36) NOT NULL,
  granted_by VARCHAR(36) NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id),
  UNIQUE KEY unique_user_role (user_id, role_id),
  INDEX idx_user (user_id),
  INDEX idx_role (role_id),
  INDEX idx_expires (expires_at)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
  id VARCHAR(36) PRIMARY KEY,
  role_id VARCHAR(36) NOT NULL,
  permission_id VARCHAR(36) NOT NULL,
  granted_by VARCHAR(36) NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id),
  UNIQUE KEY unique_role_permission (role_id, permission_id),
  INDEX idx_role (role_id),
  INDEX idx_permission (permission_id)
);

-- 用户直接权限表（临时权限）
CREATE TABLE user_permissions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  permission_id VARCHAR(36) NOT NULL,
  granted_by VARCHAR(36) NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id),
  UNIQUE KEY unique_user_permission (user_id, permission_id),
  INDEX idx_user (user_id),
  INDEX idx_permission (permission_id),
  INDEX idx_expires (expires_at)
);
```

#### **会话管理表**
```sql
-- 用户会话表
CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  refresh_token VARCHAR(255) UNIQUE NOT NULL,
  access_token_hash VARCHAR(255),
  ip_address VARCHAR(45),
  user_agent TEXT,
  device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user (user_id),
  INDEX idx_session_token (session_token),
  INDEX idx_refresh_token (refresh_token),
  INDEX idx_expires (expires_at),
  INDEX idx_active (is_active)
);

-- Token黑名单表
CREATE TABLE token_blacklist (
  id VARCHAR(36) PRIMARY KEY,
  token_hash VARCHAR(255) UNIQUE NOT NULL,
  token_type ENUM('access', 'refresh') NOT NULL,
  user_id VARCHAR(36),
  revoked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  reason VARCHAR(255),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires (expires_at)
);
```

#### **审计日志表**
```sql
-- 操作日志表
CREATE TABLE audit_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  session_id VARCHAR(36),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id VARCHAR(36),
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL,
  INDEX idx_user (user_id),
  INDEX idx_action (action),
  INDEX idx_resource (resource_type, resource_id),
  INDEX idx_created (created_at),
  INDEX idx_success (success)
);

-- 登录日志表
CREATE TABLE login_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  username VARCHAR(50),
  ip_address VARCHAR(45),
  user_agent TEXT,
  login_type ENUM('password', 'sso', 'token') DEFAULT 'password',
  success BOOLEAN NOT NULL,
  failure_reason VARCHAR(255),
  session_id VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL,
  INDEX idx_user (user_id),
  INDEX idx_username (username),
  INDEX idx_ip (ip_address),
  INDEX idx_created (created_at),
  INDEX idx_success (success)
);
```

---

## ✅ **TODO清单**

### **阶段1: 数据库设计和实现** (1周)

#### **数据库结构**
- [ ] 创建用户相关数据表
- [ ] 创建角色权限数据表
- [ ] 创建会话管理数据表
- [ ] 创建审计日志数据表
- [ ] 建立表间关系和索引
- [ ] 编写数据库迁移脚本

#### **数据模型**
- [ ] 实现TypeScript类型定义
- [ ] 创建数据访问对象(DAO)
- [ ] 实现数据验证规则
- [ ] 建立数据关系映射
- [ ] 创建种子数据脚本

### **阶段2: DataAccessManager扩展** (1周)

#### **认证模块开发**
- [ ] 实现AuthDataAccess类
- [ ] 开发TokenManager组件
- [ ] 实现SessionManager组件
- [ ] 创建PermissionCache组件
- [ ] 集成AuditLogger组件

#### **权限模块开发**
- [ ] 实现UsersDataAccess类
- [ ] 开发RolesDataAccess类
- [ ] 实现PermissionsDataAccess类
- [ ] 创建权限验证算法
- [ ] 实现权限缓存机制

#### **API客户端增强**
- [ ] 添加认证拦截器
- [ ] 实现自动Token刷新
- [ ] 创建错误处理机制
- [ ] 添加请求重试逻辑

### **阶段3: API接口实现** (1周)

#### **认证API开发**
- [ ] 实现登录API端点
- [ ] 开发登出API端点
- [ ] 创建Token刷新API
- [ ] 实现Token验证API
- [ ] 开发密码管理API

#### **用户管理API**
- [ ] 实现用户CRUD API
- [ ] 开发用户角色管理API
- [ ] 创建用户权限查询API
- [ ] 实现用户会话管理API

#### **权限管理API**
- [ ] 实现角色CRUD API
- [ ] 开发权限分配API
- [ ] 创建权限检查API
- [ ] 实现权限树查询API

### **阶段4: 中间件和安全** (1周)

#### **Next.js中间件**
- [ ] 实现认证中间件
- [ ] 开发权限验证中间件
- [ ] 创建路由保护机制
- [ ] 实现API权限检查

#### **安全增强**
- [ ] 实现Token安全存储
- [ ] 开发CSRF防护
- [ ] 创建XSS防护机制
- [ ] 实现SQL注入防护
- [ ] 添加请求频率限制

#### **监控和日志**
- [ ] 实现操作审计日志
- [ ] 开发登录日志记录
- [ ] 创建安全事件监控
- [ ] 实现性能监控

### **阶段5: 测试和优化** (1周)

#### **单元测试**
- [ ] 编写认证模块测试
- [ ] 实现权限验证测试
- [ ] 创建API接口测试
- [ ] 开发数据库操作测试

#### **集成测试**
- [ ] 实现端到端认证流程测试
- [ ] 开发权限控制集成测试
- [ ] 创建API集成测试
- [ ] 实现安全性测试

#### **性能优化**
- [ ] 优化数据库查询性能
- [ ] 实现权限缓存优化
- [ ] 优化API响应时间
- [ ] 减少内存使用

---

## 📋 **验收标准**

### **架构验收**
- ✅ DataAccessManager集成完整正确
- ✅ 中间件权限验证有效
- ✅ API接口规范符合标准
- ✅ 数据库设计合理高效

### **功能验收**
- ✅ 认证流程完整可用
- ✅ 权限验证准确有效
- ✅ 会话管理正常工作
- ✅ 审计日志记录完整

### **性能验收**
- ✅ API响应时间 < 1秒
- ✅ 数据库查询优化
- ✅ 权限缓存命中率 > 90%
- ✅ 并发处理能力 > 500用户

### **安全验收**
- ✅ 通过安全测试，无高危漏洞
- ✅ Token安全机制有效
- ✅ 权限验证无绕过风险
- ✅ 审计日志完整准确

---

**下一步**: 请查看 [05-安全性设计PRD.md](./05-安全性设计PRD.md) 了解安全性设计的详细要求。
